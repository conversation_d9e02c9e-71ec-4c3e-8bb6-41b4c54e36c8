# The Companion Project For "ZenStack: The Complete Guide"

This project is the companion sample for the [ZenStack: The Complete Guide](https://zenstack.dev/docs/the-complete-guide) documentation. It demonstrates how to build a full-stack multi-tenant Todo app using ZenStack.

### Requirements

- Email/password based signin/signup.
- Space for multi-tenancy management. Users can join multiple spaces, and a space can have many users.
- Users can be of "USER" or "ADMIN" role in a space. Space admins can manage space members.
- Users can manage Todo lists and items in spaces they belong to.
- Public Todo lists are accessible to all space members. Private lists are only accessible to the owner and space admins.

### Branches

- `main`: the final completed code for the project.
- `part1`: the code up to the completion of [Part I](https://zenstack.dev/docs/the-complete-guide/part1/).
- `part2`: the code up to the completion of [Part II](https://zenstack.dev/docs/the-complete-guide/part2/).
- `part3`: the code up to the completion of [Part III](https://zenstack.dev/docs/the-complete-guide/part3/).

### Deployed App

You can access a deployed version of the app at [https://zenstack-todo.vercel.app/](https://zenstack-todo.vercel.app/).

<a href="https://zenstack-todo.vercel.app/" target="_blank">
<picture>
<img src="https://github.com/zenstackhq/the-complete-guide-sample/assets/104139426/1af19d92-b402-4930-a4ee-50aabfd9a736">
</picture>
</a>
