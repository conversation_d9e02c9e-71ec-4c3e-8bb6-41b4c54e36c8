datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

generator js {
  provider = "prisma-client-js"
}

plugin hooks {
  provider = '@zenstackhq/tanstack-query'
  target = 'react'
  version = 'v5'
  output = 'src/lib/hooks'
}

// where users can collaborate on todo lists and items
model Space {
  id        String      @id @default(cuid())
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  name      String
  slug      String      @unique @regex('^[0-9a-zA-Z_\-]{4,16}$')
  members   SpaceUser[]
  lists     List[]
  owner     User        @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  ownerId   String      @default(auth().id)

  // require login
  @@deny('all', auth() == null)

  // everyone can create a space
  @@allow('create', true)

  // any user in the space can read the space
  @@allow('read', members?[user == auth()])

  // space admin can update and delete
  @@allow('update,delete', members?[user == auth() && role == 'ADMIN'])
}

// a join table for many-to-many relation between `Space` and `User`
model SpaceUser {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  space     Space    @relation(fields: [spaceId], references: [id], onDelete: Cascade)
  spaceId   String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
  role      String   @default("USER")

  @@unique([userId, spaceId])

  // require login
  @@deny('all', auth() == null)

  // space owner can add any one
  @@allow('create', space.owner == auth())
    
  // space admin can add anyone but not himself
  @@allow('create', auth() != user && space.members?[user == auth() && role == 'ADMIN'])

  // space admin can create/update/delete
  @@allow('update,delete', space.members?[user == auth() && role == 'ADMIN'])

  // user can read members of spaces that he's a member of
  @@allow('read', space.members?[user == auth()])
}

// user
model User {
  id          String      @id @default(cuid())
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  email       String      @unique @email
  password    String?     @password @omit
  name        String?
  spaces      SpaceUser[] // user's space membership
  lists       List[]
  todos       Todo[]
  ownedSpaces Space[] // spaces owned by the user

  // everyone can sign up
  @@allow('create', true)

  // full access by oneself
  @@allow('all', auth() == this)

  // can be read by users sharing any space
  @@allow('read', spaces?[space.members?[user == auth()]])
}

// todo list
model List {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  space     Space    @relation(fields: [spaceId], references: [id], onDelete: Cascade)
  spaceId   String
  owner     User     @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  ownerId   String   @default(auth().id) @deny('update', true)
  title     String
  private   Boolean  @default(false)
  todos     Todo[]

  // require login
  @@deny('all', auth() == null)

  // can be read by space members if not private
  @@allow('read', owner == auth() || (space.members?[user == auth()] && !private))

  // when create, owner must be set to current user, and user must be in the space
  @@allow('create,update', owner == auth() && space.members?[user == auth()])

  // can be deleted by owner
  @@allow('delete', owner == auth())
}

// todo item
model Todo {
  id          String    @id @default(cuid())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  owner       User      @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  ownerId     String    @default(auth().id) @deny('update', true)
  list        List      @relation(fields: [listId], references: [id], onDelete: Cascade)
  listId      String
  title       String
  completedAt DateTime?

  // full access if the parent list is readable
  @@allow('all', check(list, 'read'))
}
