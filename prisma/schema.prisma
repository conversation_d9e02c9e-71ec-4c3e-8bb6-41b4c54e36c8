//////////////////////////////////////////////////////////////////////////////////////////////
// DO NOT MODIFY THIS FILE                                                                  //
// This file is automatically generated by ZenStack CLI and should not be manually updated. //
//////////////////////////////////////////////////////////////////////////////////////////////

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

generator js {
  provider = "prisma-client-js"
}

model Space {
  id        String      @id() @default(cuid())
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt()
  name      String
  slug      String      @unique()
  members   SpaceUser[]
  lists     List[]
  owner     User        @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  ownerId   String
}

model SpaceUser {
  id        String   @id() @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt()
  space     Space    @relation(fields: [spaceId], references: [id], onDelete: Cascade)
  spaceId   String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
  role      String   @default("USER")

  @@unique([userId, spaceId])
}

model User {
  id          String      @id() @default(cuid())
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt()
  email       String      @unique()
  password    String?
  name        String?
  spaces      SpaceUser[]
  lists       List[]
  todos       Todo[]
  ownedSpaces Space[]
}

model List {
  id        String   @id() @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt()
  space     Space    @relation(fields: [spaceId], references: [id], onDelete: Cascade)
  spaceId   String
  owner     User     @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  ownerId   String
  title     String
  private   Boolean  @default(false)
  todos     Todo[]
}

model Todo {
  id          String    @id() @default(cuid())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt()
  owner       User      @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  ownerId     String
  list        List      @relation(fields: [listId], references: [id], onDelete: Cascade)
  listId      String
  title       String
  completedAt DateTime?
}
