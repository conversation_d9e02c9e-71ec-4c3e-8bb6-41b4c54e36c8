# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a multi-tenant Todo application built with ZenStack, Next.js, and Prisma. It's the companion sample for "ZenStack: The Complete Guide" documentation.

## Common Development Commands

```bash
# Install dependencies
npm install

# Generate ZenStack files and push database changes
npx zenstack generate
npm run db:push

# Development server
npm run dev

# Build for production
npm run build

# Database management
npm run db:push       # Push schema changes to database
npm run db:studio     # Open Prisma Studio

# Update ZenStack packages to latest
npm run up
```

## Architecture & Key Concepts

### Tech Stack
- **Framework**: Next.js 14 with App Router
- **Database**: SQLite (development) via Prisma ORM
- **Authorization**: ZenStack - schema-based access control layer on top of Prisma
- **Authentication**: NextAuth.js with credentials provider
- **UI**: TailwindCSS with DaisyUI components
- **State Management**: TanStack Query (React Query) v5

### ZenStack Schema-Based Authorization

The application uses ZenStack's schema.zmodel file as the single source of truth for both data modeling and access control. Key patterns:

1. **Access Rules**: Defined directly in the schema using `@@allow` and `@@deny` directives
2. **Enhanced Prisma Client**: Created per-request with user context at `/api/model/[...path]/route.ts`
3. **Generated Hooks**: TanStack Query hooks auto-generated in `src/lib/hooks/`

### Multi-Tenancy Model

The app implements workspace-based multi-tenancy through "Spaces":
- Users can create and join multiple Spaces
- Each Space has members with USER or ADMIN roles
- Todo lists belong to Spaces
- Private lists are owner-only, public lists are visible to all Space members

### API Architecture

The app uses a RESTful API pattern:
- **ZenStack API**: `/api/model/[...path]` - Auto-generated CRUD endpoints with built-in authorization
- **NextAuth API**: `/api/auth/[...nextauth]` - Authentication endpoints

### Key Data Flow

1. **Authentication**: Email/password → NextAuth → JWT session
2. **Authorization**: Request → Enhanced Prisma client (with user context) → ZenStack rules enforcement
3. **Data Fetching**: Components → TanStack Query hooks → ZenStack API → Database

## Project Structure

- `schema.zmodel` - ZenStack schema with models and access rules
- `prisma/schema.prisma` - Generated Prisma schema (do not edit directly)
- `src/lib/hooks/` - Generated TanStack Query hooks
- `src/app/api/model/[...path]/` - ZenStack API handler
- `src/server/` - Server-side utilities (auth, database)
- `src/app/spaces/` - Space and list management pages

## Key Development Patterns

### Working with ZenStack

1. **Schema Changes**: Always modify `schema.zmodel`, then run `npx zenstack generate`
2. **Access Control**: Test rules carefully - they're enforced at the database query level
3. **Generated Code**: Don't modify files in `src/lib/hooks/` - they're regenerated

### Authentication Context

The app uses `auth()` function in ZenStack rules to reference the current user. This is automatically injected when creating the enhanced Prisma client.

### Database Development

The project uses SQLite for development (file: `./dev.db`). For production, configure a different provider in the datasource block of schema.zmodel.