{"name": "my-todo-app1", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "npx zenstack generate && npm run db:push && next build", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev", "postinstall": "prisma generate", "lint": "next lint", "start": "next start", "up": "npm i -D zenstack@latest && npm i @zenstackhq/runtime@latest @zenstackhq/server@latest @zenstackhq/tanstack-query@latest"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.1.0", "@t3-oss/env-nextjs": "^0.7.1", "@tanstack/react-query": "^5.10.0", "@zenstackhq/runtime": "^2.10.2", "@zenstackhq/server": "^2.10.2", "@zenstackhq/tanstack-query": "^2.10.2", "nanoid": "^5.0.3", "next": "^14.0.3", "next-auth": "^4.24.5", "react": "18.2.0", "react-dom": "18.2.0", "zod": "^3.22.4"}, "devDependencies": {"@next/eslint-plugin-next": "^14.0.3", "@types/bcryptjs": "^2.4.6", "@types/eslint": "^8.44.7", "@types/node": "^18.17.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "autoprefixer": "^10.4.14", "daisyui": "^4.4.17", "eslint": "^8.54.0", "postcss": "^8.4.31", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "prisma": "^6.1.0", "tailwindcss": "^3.3.5", "typescript": "^5.1.6", "zenstack": "^2.10.2"}, "ct3aMetadata": {"initVersion": "7.24.0"}, "packageManager": "npm@9.5.1"}